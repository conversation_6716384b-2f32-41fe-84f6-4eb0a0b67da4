package ir.rahavardit.ariel.data.repository

import ir.rahavardit.ariel.data.api.RetrofitClient
import ir.rahavardit.ariel.data.model.NewEventRequest
import ir.rahavardit.ariel.utils.TokenUtils

/**
 * Repository class that handles new event-related operations.
 */
class NewEventRepository : BaseRepository() {

    private val apiService = RetrofitClient.getApiService()

    /**
     * Creates a new event.
     *
     * @param token The authentication token.
     * @param request The new event request data.
     * @return A Result containing either success or an Exception.
     */
    suspend fun createEvent(token: String, request: NewEventRequest): Result<Unit> {
        return safeApiCall {
            apiService.createEvent(TokenUtils.formatToken(token), request)
        }.map { Unit }
    }
}
